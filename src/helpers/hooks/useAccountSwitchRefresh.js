import { useEffect, useRef } from "react";
import { accountSwitchEmitter } from "../context/MultiAccountContext";

/**
 * Custom hook to automatically refresh data when account is switched
 * @param {Function} refreshCallback - Function to call when account is switched
 * @param {Array} dependencies - Dependencies array (optional)
 * @param {Object} options - Configuration options
 * @param {boolean} options.immediate - Whether to call refresh immediately on mount (default: false)
 * @param {number} options.delay - Delay in ms before calling refresh (default: 100)
 */
export const useAccountSwitchRefresh = (
  refreshCallback,
  dependencies = [],
  options = {}
) => {
  const { immediate = false, delay = 100 } = options;
  const callbackRef = useRef(refreshCallback);
  const timeoutRef = useRef(null);

  // Update callback ref when it changes
  useEffect(() => {
    callbackRef.current = refreshCallback;
  }, [refreshCallback]);

  useEffect(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    const handleAccountSwitch = (eventData) => {
      console.log("Account switch detected, refreshing data...", eventData);

      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Add a small delay to ensure the new token is properly set
      timeoutRef.current = setTimeout(() => {
        if (callbackRef.current && typeof callbackRef.current === "function") {
          try {
            callbackRef.current(eventData);
          } catch (error) {
            console.error("Error in account switch refresh callback:", error);
          }
        }
      }, delay);
    };

    // Subscribe to account switch events
    const unsubscribe = accountSwitchEmitter.subscribe(handleAccountSwitch);

    // Call immediately if requested
    if (immediate && callbackRef.current) {
      callbackRef.current({ action: "INITIAL_LOAD" });
    }

    // Cleanup function
    return () => {
      unsubscribe();
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, dependencies);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
};

/**
 * Hook for Redux actions that need to be dispatched on account switch
 * @param {Function} dispatch - Redux dispatch function
 * @param {Array} actions - Array of action creators to dispatch
 * @param {Array} dependencies - Dependencies array (optional)
 */
export const useAccountSwitchReduxRefresh = (
  dispatch,
  actions = [],
  dependencies = []
) => {
  const refreshCallback = () => {
    actions.forEach((action) => {
      if (typeof action === "function") {
        dispatch(action());
      } else {
        dispatch(action);
      }
    });
  };

  useAccountSwitchRefresh(refreshCallback, [dispatch, ...dependencies]);
};

/**
 * Hook for API calls that need to be refreshed on account switch
 * @param {Array} apiCalls - Array of API call functions
 * @param {Array} dependencies - Dependencies array (optional)
 * @param {Object} options - Configuration options
 */
export const useAccountSwitchApiRefresh = (
  apiCalls = [],
  dependencies = [],
  options = {}
) => {
  const refreshCallback = async () => {
    try {
      // Execute all API calls in parallel
      await Promise.all(
        apiCalls.map(async (apiCall) => {
          if (typeof apiCall === "function") {
            return await apiCall();
          }
          return Promise.resolve();
        })
      );
    } catch (error) {
      console.error("Error refreshing APIs on account switch:", error);
    }
  };

  useAccountSwitchRefresh(refreshCallback, dependencies, options);
};

export default useAccountSwitchRefresh;
